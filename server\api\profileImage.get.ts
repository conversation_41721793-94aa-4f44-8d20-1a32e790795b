interface userProfile {
  profile_image: string;
  username: string;
}

//https://api.flizaos.com/get_profile_image?username=flizaos
export default defineEventHandler(async (event) => {
  const { username } = getQuery(event);
  console.log('get profile image for', username)

  // Here you can perform any server-side logic, like fetching data or processing input
  const users: userProfile = await $fetch<userProfile>('https://api.flizaos.com/get_profile_image?username=' + username)
    .catch((error) => {
      console.error('Error fetching profile image:', error);
      return {
        profile_image: 'https://example.com/default-image.png',
        username: username as string
      }; // Fallback image with username
    });
  return {
    get_profile_image: users.profile_image || 'https://example.com/default-image.png',
  };
});
