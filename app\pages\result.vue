<template>
  <div class="min-h-screen p-4 sm:p-6">
    <div class="mx-auto max-w-7xl">
      <!-- 标题区域 -->
      <div class="mb-6 text-center sm:mb-8">
        <h1 class="mb-2 text-xl font-bold text-gray-800 sm:text-2xl">
          Estimate your academic ability
        </h1>
        <div class="text-xs text-gray-600 sm:text-sm">
          Enter your X account to see which mutual followers have the same academic ability as you.
        </div>
      </div>

      <!-- 用户名输入框 -->
      <div class="flex justify-center mb-6 sm:mb-8">
        <div class="relative w-full ">
          <UInput
                  v-model="currentUsername"
                  size="lg"
                  class="w-full"
                  :ui="{
                    base: 'relative block disabled:cursor-not-allowed disabled:opacity-75 focus:outline-none h-[56px] sm:h-[64px] flex justify-between items-center bg-white rounded-[80px] border-[4px] border-[#F2F2F259] shadow-[0px_4px_24px_0px_#1226420D] px-4 sm:px-6 py-3',
                    rounded: 'rounded-[80px]',
                    placeholder: 'placeholder-gray-400 dark:placeholder-gray-500',
                    size: {
                      lg: 'text-sm sm:text-base'
                    },
                    color: {
                      white: {
                        outline: ''
                      }
                    }
                  }">
            <template #prefix>
              <p class="text-sm sm:text-base">@</p>
            </template>
            <template #trailing>
              <div class="flex items-center space-x-1">
                <UButton
                         @click="handleSubmit"
                         :disabled="!currentUsername.trim()"
                         variant="ghost"
                         class=""
                         :ui="{ base: 'cursor-pointer hover:bg-transparent focus-visible:ring-0 focus-visible:ring-transparent', rounded: 'rounded-full' }">
                  <img src="~/assets/images/ai.png" alt="AI Icon" class="w-6 h-6 sm:w-8 sm:h-8"></img>
                </UButton>
                <!-- 灰色分隔线 -->
                <div class="w-2 h-6 bg-[#F2F2F2]"></div>
                <UButton
                         variant="ghost"
                         class="hidden sm:inline-flex"
                         :ui="{ base: 'cursor-pointer hover:bg-transparent focus-visible:ring-0 focus-visible:ring-transparent', rounded: 'rounded-full' }">
                  <img src="~/assets/images/x.png" alt="X Icon" class="w-6 h-6 sm:w-8 sm:h-8"></img>
                </UButton>

                <UButton
                         variant="ghost"
                         class="hidden sm:inline-flex"
                         :ui="{ base: 'cursor-pointer hover:bg-transparent focus-visible:ring-0 focus-visible:ring-transparent', rounded: 'rounded-full' }">
                  <img src="~/assets/images/tg.png" alt="Telegram Icon" class="w-6 h-6 sm:w-8 sm:h-8"></img>
                </UButton>

                <UButton
                         variant="ghost"
                         class="hidden sm:inline-flex"
                         :ui="{ base: 'cursor-pointer hover:bg-transparent focus-visible:ring-0 focus-visible:ring-transparent', rounded: 'rounded-full' }">
                  <img src="~/assets/images/github.png" alt="GitHub Icon" class="w-6 h-6 sm:w-8 sm:h-8"></img>
                </UButton>
              </div>
            </template>
          </UInput>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div class="space-y-4 sm:space-y-6 lg:grid lg:grid-cols-2 lg:gap-6 lg:space-y-0">
        <!-- 雷达图区域 -->
        <div style="height: 97%;"
             class="shadow-lg bg-transparent border border-solid border-[#fff] border-width-2 backdrop-blur-sm rounded-3xl ">
          <!-- 动态雷达图组件 -->
          <RadarChart :username="currentUsername" :image="image" :data="radarData" :labels="radarLabels"
                      :max-value="10" />

          <!-- 底部说明文字 -->
          <div class="mt-4 p-4 sm:p-6 bg-[#008CFF26]  leading-5 sm:leading-6 w-full h-32 text-xs text-left text-[#162D4E] border-t border-solid border-[#fff] border-width-2 font-bold
               rounded-bl-3xl rounded-br-3xl
               ">
            {{ overall_assessment }}
          </div>
        </div>

        <!-- 右侧面板 -->
        <div class="flex flex-col space-y-4">
          <!-- 综合分数卡片 -->
          <div
               class="p-4 sm:p-6 shadow-lg bg-transparent border border-solid border-[#fff] border-width-2 backdrop-blur-sm rounded-3xl">
            <div
                 class="flex-1/3  flex flex-col sm:flex-row sm:items-center sm:justify-between pb-4 mb-4 sm:mb-6 border-b border-[#008CFF26] space-y-2 sm:space-y-0">
              <div>
                <h3 class="text-base font-semibold text-gray-800 sm:text-lg">Comprehensive score</h3>
              </div>
            </div>

            <div
                 class="flex flex-col gap-3 mb-4 space-y-4 sm:flex-row sm:items-center sm:justify-between sm:mb-6 sm:space-y-0">
              <!-- 分数显示 - 红色胶囊形状 -->
              <div
                   class="flex items-center justify-center gap-8 flex-[0.98] p-6 text-white rounded-xl shadow-lg bg-[#FF3314] w-full sm:w-56 text-center">
                <span class="text-5xl font-bold">{{ comprehensiveScore }}</span>
                <div class="flex flex-col items-start">
                  <p>1</p>
                  <p>Scholar</p>
                </div>
              </div>

              <!-- 徽章组 -->
              <div
                   class="flex-1 flex justify-center sm:justify-start space-x-1 sm:space-x-2  bg-white/35 border-[#fff] rounded-xl p-6">
                <div v-for="(item, index) in source" :key="index"
                     class="flex items-center justify-center w-12 h-12 rounded-full">
                  <img src="~/assets/images/hz.png" alt="Ibis Icon">
                </div>
                <div v-for="(item, index) in source1" :key="index"
                     class="flex items-center justify-center w-12 h-12 rounded-full">
                  <img src="~/assets/images/hz1.png" alt="Ibis Icon">
                </div>
              </div>
            </div>

            <div class="mt-4">
              <div class="mb-1 text-sm font-medium text-gray-800">Save your diagram</div>
              <div class="mb-4 text-xs text-gray-500">
                Your image will be processed locally - nothing is uploaded to our servers
              </div>
              <UButton block size="lg" class="py-4 font-medium text-[#000000] border-0 shadow-lg bg-gradient-to-r hover:bg-[#008CFF80]
                       bg-[#008CFF59] rounded-2xl" :ui="{
                        base: 'cursor-pointer focus:outline-none focus-visible:outline-0 disabled:cursor-not-allowed disabled:opacity-75 flex-shrink-0',
                        font: 'font-medium',
                        rounded: 'rounded-2xl',
                        size: {
                          lg: 'text-sm sm:text-base px-4 sm:px-6 py-3'
                        }
                      }">
                <img src="@/assets/images/down.png" alt="" class="w-6 h-6 mr-2">
                Export Avatar
              </UButton>
            </div>
          </div>

          <!-- 分享卡片 -->
          <div
               class=" p-4 sm:p-6 shadow-lg bg-transparent border border-solid border-[#fff] border-width-2 backdrop-blur-sm rounded-3xl">
            <div class="mb-4 text-sm font-medium text-gray-800">Share on Twitter</div>
            <UButton block size="lg"
                     class="py-4 font-medium text-white border-0 shadow-lg bg-gradient-to-r rounded-2xl bg-[#008CFF] hover:bg-[#008CFF90] hover:shadow-lg"
                     :ui="{
                      base: 'cursor-pointer focus:outline-none focus-visible:outline-0 disabled:cursor-not-allowed disabled:opacity-75 flex-shrink-0',
                      font: 'font-medium',
                      rounded: 'rounded-2xl',
                      size: {
                        lg: 'text-sm sm:text-base px-4 sm:px-6 py-3'
                      }
                    }">
              <img src="@/assets/images/share.png" alt="" class="w-6 h-6 mr-2">
              Click to share
            </UButton>
          </div>

          <!-- 移动端底部导航 -->
          <div class="flex justify-center mb-10 space-x-4 sm:hidden">
            <UButton variant="ghost" class="p-3"
                     :ui="{ base: 'cursor-pointer hover:bg-white/20 focus-visible:ring-0 focus-visible:ring-transparent', rounded: 'rounded-full' }">
              <img src="~/assets/images/x.png" alt="X Icon" class="w-8 h-8"></img>
            </UButton>
            <UButton variant="ghost" class="p-3"
                     :ui="{ base: 'cursor-pointer hover:bg-white/20 focus-visible:ring-0 focus-visible:ring-transparent', rounded: 'rounded-full' }">
              <img src="~/assets/images/tg.png" alt="Telegram Icon" class="w-8 h-8"></img>
            </UButton>
            <UButton variant="ghost" class="p-3"
                     :ui="{ base: 'cursor-pointer hover:bg-white/20 focus-visible:ring-0 focus-visible:ring-transparent', rounded: 'rounded-full' }">
              <img src="~/assets/images/github.png" alt="GitHub Icon" class="w-8 h-8"></img>
            </UButton>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'

const toast = useToast() // 建议添加一个提示工具


const route = useRoute()
const currentUsername = ref(route.query.username || '')
const image = decodeURIComponent(route.query.image || '')
const overall_assessment = ref("")
const comprehensiveScore = ref(0)
const source = ref(0)
const source1 = ref(6)

// 雷达图数据配置
const radarLabels = ref([])
const radarData = ref([])

const { data, pending, error, execute } = await useFetch('/api/evaluateAcademicLevel', {
  query: {
    username: currentUsername,
  },
  immediate: false, // 不在组件加载时立即执行
})

// 5. 监听请求返回的数据，并更新到雷达图
watch(data, (newData) => {
  if (newData && newData.evaluation) {
    // 假设 API 返回的数据结构是 { result: { labels: [...], data: [...] } }
    console.log(newData);
    let evaluation = newData.evaluation || {}
    //interest_exploration
    let radarLabelData = [];
    let radarDataValues = [];
    radarLabelData.push("Interest")
    radarDataValues.push(evaluation.interest_exploration.score || 0) // 确保有默认值
    radarLabelData.push("Expression")
    radarDataValues.push(evaluation.language_expression.score || 0) // 确保
    radarLabelData.push("Knowledge")
    radarDataValues.push(evaluation.knowledge_base.score || 0) // 确保有默认值
    radarLabelData.push("Total score")
    radarDataValues.push(evaluation.total_score || 0) // 确保有默认值
    radarLabelData.push("Technical")
    radarDataValues.push(evaluation.technical_expertise.score || 0) // 确保有默认值
    radarLabelData.push("Learning")
    radarDataValues.push(evaluation.learning_ability.score || 0) // 确保有默认值

    overall_assessment.value = evaluation.overall_assessment || "No assessment available"
    comprehensiveScore.value = evaluation.total_score || 0
    radarLabels.value = radarLabelData
    radarData.value = radarDataValues
    if (comprehensiveScore.value >= 0 && comprehensiveScore.value <= 3) {
      source.value = 1 // 如果评估分数在0-3范围内，则显示1个徽章
      source1.value = 5
    } else if (comprehensiveScore.value > 3) {
      switch (comprehensiveScore.value) {
        case 4:
          source.value = 2 // 如果评估分数在4-5范围内，则显示2个徽章
          source1.value = 4
          break;
        case 5:
          source.value = 3 // 如果评估分数在5-6范围内，则
          source1.value = 3
          break;
        case 6:
          source.value = 4 // 如果评估分数在6-7范围内，则显示3个徽章
          source1.value = 2
          break;
        case 7:
          source.value = 5 // 如果评估分数在7-8范围内，则
          source1.value = 1
          break;
        default:
          source.value = 6 // 如果评估分数在8-9范围内，则显示4个徽章
          source1.value = 0
          break;
      }
    }
    toast.add({ title: '评估成功！', color: 'green' })
  }
})

// 监听错误
watch(error, (newError) => {
  if (newError) {
    toast.add({ title: '评估失败', description: newError.message, color: 'red' })
  }
})

// 处理提交
const handleSubmit = () => {
  if (!currentUsername.value.trim()) {
    toast.add({ title: '请输入一个有效的用户名', color: 'orange' })
    return
  }
  execute()
}

// 组件挂载时初始化数据
if (currentUsername.value) {
  handleSubmit()
}
</script>
