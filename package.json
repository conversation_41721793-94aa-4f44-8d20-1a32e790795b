{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@nuxt/ui": "^3.3.0", "@tailwindcss/vite": "^4.1.11", "echarts": "^6.0.0", "nuxt": "^4.0.1", "tailwindcss": "^4.1.11", "vue": "^3.5.18", "vue-echarts": "^7.0.3", "vue-router": "^4.5.1"}, "devDependencies": {"sass": "^1.89.2"}}