<template>
  <div class="relative flex items-center justify-center m-auto h-90"
       :style="{ transform: `scale(${responsiveScale})` }">
    <!-- 数据加载中的占位符 -->
    <div v-if="!isDataReady" class="flex items-center justify-center w-full h-full">
      <div class="flex flex-col items-center justify-center space-y-4">
        <div class="w-16 h-16 border-4 border-blue-200 rounded-full border-t-blue-500 animate-spin"></div>
        <p class="text-sm text-gray-500">正在加载数据...</p>
      </div>
    </div>

    <!-- ECharts 雷达图容器 -->
    <div v-else ref="chartContainer" class="w-full h-full"></div>

    <!-- 中心图片 -->
    <div v-if="isDataReady" class="absolute inset-0 flex items-center justify-center pointer-events-none">
      <div
           class="flex items-center justify-center w-12 h-12 overflow-hidden bg-white border-[#008CFF] shadow-xl rounded-xs border-3">
        <img :src="image" alt="Profile" class="object-cover w-full h-full" @error="showFallback = true"
             v-if="!showFallback" />
        <!-- 图片加载失败时的回退显示 -->
        <div v-else class="flex items-center justify-center w-full h-full bg-gradient-to-br from-blue-500 to-blue-600">
          <span class="text-xl font-bold text-white">{{ username.charAt(0).toUpperCase() }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'

// Props 定义
interface Props {
  username: string
  image: string
  data?: number[]
  labels?: string[]
  maxValue?: number
}

const props = withDefaults(defineProps<Props>(), {
  data: () => [],
  labels: () => [],
  maxValue: 10,
  image: '',
})

// 响应式引用
const chartContainer = ref<HTMLElement>()
const showFallback = ref(false)
const windowWidth = ref(typeof window !== 'undefined' ? window.innerWidth : 1024)
const isChartInitialized = ref(false)
let chartInstance: any = null

// 计算响应式缩放比例
const responsiveScale = computed(() => {
  if (windowWidth.value < 480) return 0.7 // 小手机
  if (windowWidth.value < 768) return 0.8 // 大手机/小平板
  if (windowWidth.value < 1024) return 0.9 // 平板
  return 1.0 // 桌面端
})

// 判断数据是否准备就绪
const isDataReady = computed(() => {
  return Array.isArray(props.data) && props.data.length > 0 && props.data.some((value) => typeof value === 'number' && value > 0) && Array.isArray(props.labels) && props.labels.length > 0
})

// 动态导入 ECharts
const initChart = async () => {
  try {
    // 检查数据是否准备就绪
    if (!isDataReady.value) {
      console.log('数据未准备就绪，跳过图表初始化')
      return
    }

    // 动态导入 ECharts
    const echarts = await import('echarts')

    if (!chartContainer.value) return

    // 如果已经初始化过，先销毁
    if (chartInstance) {
      chartInstance.dispose()
      chartInstance = null
    }

    // 初始化图表实例
    chartInstance = echarts.init(chartContainer.value)
    isChartInitialized.value = true

    // 配置选项
    const option = {
      backgroundColor: 'transparent',
      // 隐藏图表标题
      title: {
        text: null,
      },

      radar: {
        // 雷达图配置 - 六边形形状
        shape: 'polygon', // 多边形
        splitNumber: 3, // 分割层数
        radius: '70%',
        center: ['50%', '50%'],

        // 指示器名称和指示器轴的距离
        nameGap: 5,

        // 设置外圈文字样式 - 蓝色圆角背景
        axisName: {
          color: '#333', // 白色文字
          backgroundColor: '#fff', // 蓝色背景
          borderRadius: 8, // 圆角
          //border color
          borderColor: 'red', // 边框颜色
          padding: [6, 12], // 内边距 [上下, 左右]
          fontSize: 12,
          fontWeight: 'bold',
        },

        // 坐标轴配置
        axisLine: {
          lineStyle: {
            color: 'rgba(211, 222, 232, 0.8)',
            width: 1,
          },
        },

        // 分割线配置 - 虚线
        splitLine: {
          lineStyle: {
            color: 'rgba(211, 222, 232, 0.8)',
            width: 2,
          },
          // 虚线样式
          type: 'dashed',
        },

        // 分割区域配置 - 交替背景色
        splitArea: {
          areaStyle: {
            color: ['#c2daf2', '#d6e5f5', '#cadcf0'],
          },
        },
        // 指示器配置 - 5个点对应六边形
        indicator: props.labels.map((label) => ({
          name: label, // 显示SCAI标签
          max: props.maxValue,
          axisLabel: {
            show: false,
          },
        })),

        // 轴标签配置
        axisLabel: {
          show: false,
        },
      },

      // 系列数据
      series: [
        {
          name: 'SCAI评估',
          type: 'radar',

          // 数据点标记样式
          symbol: 'circle',
          symbolSize: 8,

          // 数据配置
          data: [
            {
              value: props.data,
              name: 'SCAI能力评估',

              // 区域样式 - 半透明蓝色填充
              areaStyle: {
                color: '#5a9fe2',
                opacity: 0.6,
              },

              // 线条样式
              lineStyle: {
                color: '#5a9fe2',
                width: 3,
              },

              // 数据点样式
              itemStyle: {
                color: '#3a87d0',
                borderColor: '#ffffff',
                borderWidth: 2,
              },
            },
          ],

          // 动画配置
          animationDuration: 1200,
          animationEasing: 'cubicOut',
          animationDelay: 200,
        },
      ],

      // 全局颜色配置
      color: ['#5a9fe2', '#3a87d0', '#61a5e8'],

      // 全局动画配置
      animation: true,
      animationDuration: 1200,
      animationEasing: 'cubicOut',
      animationThreshold: 2000,

      // 图例配置（如果需要的话）
      legend: {
        show: false, // 隐藏图例，因为我们使用自定义标签
      },
    }

    // 设置配置并渲染
    chartInstance.setOption(option)

    // 响应式调整
    const resizeObserver = new ResizeObserver(() => {
      chartInstance?.resize()
    })
    resizeObserver.observe(chartContainer.value)

    // 窗口大小变化监听
    const handleResize = () => {
      windowWidth.value = window.innerWidth
    }

    window.addEventListener('resize', handleResize)

    // 组件卸载时清理
    onUnmounted(() => {
      resizeObserver.disconnect()
      window.removeEventListener('resize', handleResize)
      chartInstance?.dispose()
    })
  } catch (error) {
    console.error('Failed to load ECharts:', error)
    // 如果 ECharts 加载失败，回退到静态 SVG
    await fallbackToStaticSVG()
  }
}

// 回退到静态 SVG 的方法 - 五芒星形状
const fallbackToStaticSVG = async () => {
  if (!chartContainer.value) return

  // 创建静态 SVG 作为回退方案 - 五芒星形状
  chartContainer.value.innerHTML = `
    <svg class="w-full h-full" viewBox="0 0 300 300">
      <!-- 定义渐变 -->
      <defs>
        <radialGradient id="radarGradient" cx="50%" cy="50%" r="80%">
          <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:0.8" />
          <stop offset="70%" style="stop-color:#60a5fa;stop-opacity:0.6" />
          <stop offset="100%" style="stop-color:#93c5fd;stop-opacity:0.4" />
        </radialGradient>
        <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
          <feDropShadow dx="0" dy="2" stdDeviation="3" flood-color="#1e40af" flood-opacity="0.3"/>
        </filter>
      </defs>

      <!-- 背景五芒星网格 -->
      <g stroke="#e2e8f0" stroke-width="1" fill="none" opacity="0.5">
        <!-- 外层五芒星 -->
        <polygon points="150,50 221,108 192,192 108,192 79,108" />
        <!-- 中层五芒星 -->
        <polygon points="150,80 192,123 177,177 123,177 108,123" />
        <!-- 内层五芒星 -->
        <polygon points="150,110 171,138 162,162 138,162 129,138" />

        <!-- 从中心到各顶点的连线 -->
        <line x1="150" y1="150" x2="150" y2="50" />
        <line x1="150" y1="150" x2="221" y2="108" />
        <line x1="150" y1="150" x2="192" y2="192" />
        <line x1="150" y1="150" x2="108" y2="192" />
        <line x1="150" y1="150" x2="79" y2="108" />
      </g>

      <!-- 数据区域 - 五芒星形状 -->
      <polygon points="150,70 200,115 180,180 120,180 100,115"
               fill="url(#radarGradient)"
               stroke="#2563eb"
               stroke-width="2.5"
               filter="url(#shadow)" />

      <!-- 数据点 -->
      <circle cx="150" cy="70" r="5" fill="#1e40af" stroke="#ffffff" stroke-width="3" filter="url(#shadow)" />
      <circle cx="200" cy="115" r="5" fill="#1e40af" stroke="#ffffff" stroke-width="3" filter="url(#shadow)" />
      <circle cx="180" cy="180" r="5" fill="#1e40af" stroke="#ffffff" stroke-width="3" filter="url(#shadow)" />
      <circle cx="120" cy="180" r="5" fill="#1e40af" stroke="#ffffff" stroke-width="3" filter="url(#shadow)" />
      <circle cx="100" cy="115" r="5" fill="#1e40af" stroke="#ffffff" stroke-width="3" filter="url(#shadow)" />
    </svg>
  `
}

// 更新图表数据的方法
const updateChart = () => {
  if (!chartInstance || !isChartInitialized.value || !isDataReady.value) {
    console.log('图表未初始化或数据未准备就绪，跳过更新')
    return
  }

  console.log('更新雷达图数据:', props.data) // 调试日志

  const option = {
    radar: {
      indicator: props.labels.map(() => ({
        name: '',
        max: props.maxValue,
      })),
    },
    series: [
      {
        name: 'SCAI评估',
        type: 'radar',
        data: [
          {
            value: props.data,
            name: 'SCAI能力值',
          },
        ],
      },
    ],
  }

  // 强制重新渲染，不合并配置
  chartInstance.setOption(option, {
    notMerge: true, // 不合并，完全替换
    replaceMerge: ['series'],
    silent: false,
  })

  // 强制重绘
  chartInstance.resize()
}

// 强制刷新图表的方法
const forceRefresh = () => {
  if (!isDataReady.value) {
    console.log('数据未准备就绪，跳过强制刷新')
    return
  }

  console.log('强制刷新雷达图，当前数据:', props.data)

  // 清除当前配置
  if (chartInstance) {
    chartInstance.clear()
  }

  // 重置初始化状态
  isChartInitialized.value = false

  // 重新初始化
  setTimeout(() => {
    initChart()
  }, 100)
}

// 监听数据准备状态变化
watch(
  isDataReady,
  async (ready) => {
    console.log('数据准备状态变化:', ready)
    if (ready && !isChartInitialized.value) {
      console.log('数据准备就绪，开始初始化图表')
      await nextTick()
      await initChart()
    }
  },
  { immediate: true }
)

// 监听数据变化
watch(
  () => props.data,
  (newData, oldData) => {
    console.log('数据变化:', { oldData, newData })
    if (isChartInitialized.value && isDataReady.value) {
      updateChart()
    }
  },
  { deep: true, immediate: false }
)

watch(
  () => props.labels,
  () => {
    if (isChartInitialized.value && isDataReady.value) {
      updateChart()
    }
  },
  { deep: true }
)

watch(
  () => props.maxValue,
  () => {
    if (isChartInitialized.value && isDataReady.value) {
      updateChart()
    }
  }
)

// 暴露刷新方法给父组件使用
defineExpose({
  forceRefresh,
  updateChart,
})

// 组件挂载时初始化
onMounted(async () => {
  // 初始化窗口大小
  if (typeof window !== 'undefined') {
    windowWidth.value = window.innerWidth
  }

  await nextTick()
  // 不立即初始化图表，等待数据准备就绪
  console.log('组件挂载完成，等待数据...')
})
</script>
