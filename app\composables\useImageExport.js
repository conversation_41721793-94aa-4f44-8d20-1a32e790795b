/**
 * 图片导出功能 Composable
 * 将 ECharts 图表和用户头像合成为图片并导出
 */

export const useImageExport = () => {
  const toast = useToast()

  /**
   * 加载图片并处理跨域问题
   * @param {string} src - 图片源地址
   * @returns {Promise<HTMLImageElement>} 加载完成的图片对象
   */
  const loadImage = (src) => {
    return new Promise((resolve, reject) => {
      const img = new Image()
      img.crossOrigin = 'anonymous' // 处理跨域问题

      img.onload = () => resolve(img)
      img.onerror = (error) => {
        console.error('图片加载失败:', src, error)
        reject(new Error(`图片加载失败: ${src}`))
      }

      // 如果是相对路径或本地路径，直接使用
      // 如果是外部链接，可能需要代理处理
      img.src = src
    })
  }

  /**
   * 创建渐变背景
   * @param {CanvasRenderingContext2D} ctx - Canvas 上下文
   * @param {number} width - 画布宽度
   * @param {number} height - 画布高度
   */
  const drawBackground = (ctx, width, height) => {
    // 先填充白色背景
    ctx.fillStyle = '#FFFFFF'
    ctx.fillRect(0, 0, width, height)

    // 创建线性渐变，角度为208.19度
    // 将角度转换为弧度并计算起点和终点
    const angle = (208.19 * Math.PI) / 180
    const diagonal = Math.sqrt(width * width + height * height)
    const centerX = width / 2
    const centerY = height / 2

    // 计算渐变的起点和终点
    const startX = centerX - (diagonal / 2) * Math.cos(angle)
    const startY = centerY - (diagonal / 2) * Math.sin(angle)
    const endX = centerX + (diagonal / 2) * Math.cos(angle)
    const endY = centerY + (diagonal / 2) * Math.sin(angle)

    // 创建线性渐变
    const gradient = ctx.createLinearGradient(startX, startY, endX, endY)

    // 添加渐变色停
    gradient.addColorStop(0.1168, 'rgba(32, 155, 255, 0.65)') // 11.68%
    gradient.addColorStop(0.4704, 'rgba(63, 181, 247, 0.4)') // 47.04%
    gradient.addColorStop(0.8255, 'rgba(239, 194, 188, 0.35)') // 82.55%

    ctx.fillStyle = gradient
    ctx.fillRect(0, 0, width, height)
  }

  /**
   * 在 Canvas 上绘制圆角矩形
   * @param {CanvasRenderingContext2D} ctx - Canvas 上下文
   * @param {number} x - X 坐标
   * @param {number} y - Y 坐标
   * @param {number} width - 宽度
   * @param {number} height - 高度
   * @param {number} radius - 圆角半径
   */
  const drawRoundedRect = (ctx, x, y, width, height, radius) => {
    ctx.beginPath()
    ctx.moveTo(x + radius, y)
    ctx.lineTo(x + width - radius, y)
    ctx.quadraticCurveTo(x + width, y, x + width, y + radius)
    ctx.lineTo(x + width, y + height - radius)
    ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height)
    ctx.lineTo(x + radius, y + height)
    ctx.quadraticCurveTo(x, y + height, x, y + height - radius)
    ctx.lineTo(x, y + radius)
    ctx.quadraticCurveTo(x, y, x + radius, y)
    ctx.closePath()
  }

  /**
   * 绘制用户头像
   * @param {CanvasRenderingContext2D} ctx - Canvas 上下文
   * @param {HTMLImageElement} avatarImg - 头像图片
   * @param {number} canvasWidth - 画布宽度
   * @param {number} canvasHeight - 画布高度
   * @param {string} username - 用户名（作为备选显示）
   */
  const drawAvatar = (ctx, avatarImg, canvasWidth, canvasHeight, username = '') => {
    const avatarSize = 80 // 头像大小
    const avatarX = (canvasWidth - avatarSize) / 2
    const avatarY = (canvasHeight - avatarSize) / 2

    // 保存当前状态
    ctx.save()

    try {
      if (avatarImg) {
        // 创建圆形裁剪路径
        ctx.beginPath()
        ctx.arc(avatarX + avatarSize / 2, avatarY + avatarSize / 2, avatarSize / 2, 0, Math.PI * 2)
        ctx.clip()

        // 绘制头像
        ctx.drawImage(avatarImg, avatarX, avatarY, avatarSize, avatarSize)

        // 恢复状态
        ctx.restore()

        // 绘制头像边框
        ctx.beginPath()
        ctx.arc(avatarX + avatarSize / 2, avatarY + avatarSize / 2, avatarSize / 2, 0, Math.PI * 2)
        ctx.strokeStyle = '#008CFF'
        ctx.lineWidth = 4
        ctx.stroke()
      } else {
        // 头像加载失败时的备选方案
        ctx.restore()

        // 绘制圆形背景
        ctx.beginPath()
        ctx.arc(avatarX + avatarSize / 2, avatarY + avatarSize / 2, avatarSize / 2, 0, Math.PI * 2)
        ctx.fillStyle = '#008CFF'
        ctx.fill()

        // 绘制用户名首字母
        ctx.fillStyle = '#FFFFFF'
        ctx.font = 'bold 32px Arial'
        ctx.textAlign = 'center'
        ctx.textBaseline = 'middle'
        const initial = username ? username.charAt(0).toUpperCase() : '?'
        ctx.fillText(initial, avatarX + avatarSize / 2, avatarY + avatarSize / 2)
      }
    } catch (error) {
      console.error('绘制头像失败:', error)
      ctx.restore()
    }
  }

  /**
   * 生成图片Canvas（不下载）
   * @param {Object} options - 导出选项
   * @param {Object} options.chartInstance - ECharts 实例
   * @param {string} options.avatarSrc - 头像图片源
   * @param {string} options.username - 用户名
   * @param {Object} options.userData - 用户数据
   * @param {string} options.format - 导出格式 ('png' | 'jpeg')
   * @param {number} options.quality - 图片质量 (0-1)
   * @returns {Promise<HTMLCanvasElement|null>} 生成的Canvas元素
   */
  const generateImageCanvas = async (options) => {
    const { chartInstance, avatarSrc, username = '', userData = {}, format = 'png', quality = 0.9 } = options

    try {
      if (!chartInstance) {
        throw new Error('ECharts 实例不存在')
      }

      // 设置画布尺寸 - 使用正方形比例避免图表变形
      const canvasWidth = 800
      const canvasHeight = 800

      // 创建主画布
      const canvas = document.createElement('canvas')
      canvas.width = canvasWidth
      canvas.height = canvasHeight
      const ctx = canvas.getContext('2d')

      // 绘制背景
      drawBackground(ctx, canvasWidth, canvasHeight)

      // 获取 ECharts 图表的 DataURL
      const chartDataURL = chartInstance.getDataURL({
        type: format,
        pixelRatio: 2, // 高分辨率
        backgroundColor: 'transparent',
      })

      // 加载图表图片
      const chartImg = await loadImage(chartDataURL)

      // 计算图表绘制位置（居中，留出头像空间）
      const chartScale = 1 // 图表缩放比例
      const chartWidth = canvasWidth * chartScale
      const chartHeight = canvasHeight * chartScale
      const chartX = (canvasWidth - chartWidth) / 2
      const chartY = (canvasHeight - chartHeight) / 2

      // 绘制图表
      ctx.drawImage(chartImg, chartX, chartY, chartWidth, chartHeight)

      // 加载并绘制头像
      let avatarImg = null
      if (avatarSrc) {
        try {
          avatarImg = await loadImage(avatarSrc)
        } catch (error) {
          console.warn('头像加载失败，使用备选方案:', error)
        }
      }

      drawAvatar(ctx, avatarImg, canvasWidth, canvasHeight, username)

      // 添加标题文字（可选）
      if (userData.score !== undefined) {
        ctx.fillStyle = '#162D4E'
        ctx.font = 'bold 24px Arial'
        ctx.textAlign = 'center'
        ctx.fillText(`学术能力评估: ${userData.score}/10 分`, canvasWidth / 2, 50)
      }

      return canvas
    } catch (error) {
      console.error('生成图片Canvas失败:', error)
      return null
    }
  }

  /**
   * 导出图片主方法
   * @param {Object} options - 导出选项
   * @param {Object} options.chartInstance - ECharts 实例
   * @param {string} options.avatarSrc - 头像图片源
   * @param {string} options.username - 用户名
   * @param {Object} options.userData - 用户数据
   * @param {string} options.format - 导出格式 ('png' | 'jpeg')
   * @param {number} options.quality - 图片质量 (0-1)
   * @returns {Promise<boolean>} 导出是否成功
   */
  const exportAsImage = async (options) => {
    const { chartInstance, avatarSrc, username = '', userData = {}, format = 'png', quality = 0.9 } = options

    try {
      if (!chartInstance) {
        throw new Error('ECharts 实例不存在')
      }

      // 显示加载提示
      toast.add({
        title: '正在生成图片...',
        description: '请稍候',
        color: 'blue',
        timeout: 2000,
      })

      // 设置画布尺寸
      const canvasWidth = 800
      const canvasHeight = 600

      // 创建主画布
      const canvas = document.createElement('canvas')
      canvas.width = canvasWidth
      canvas.height = canvasHeight
      const ctx = canvas.getContext('2d')

      // 绘制背景
      drawBackground(ctx, canvasWidth, canvasHeight)

      // 获取 ECharts 图表的 DataURL
      const chartDataURL = chartInstance.getDataURL({
        type: format,
        pixelRatio: 2, // 高分辨率
        backgroundColor: 'transparent',
      })

      // 加载图表图片
      const chartImg = await loadImage(chartDataURL)

      // 计算图表绘制位置（居中，保持原始比例）
      const chartScale = 0.75 // 图表缩放比例，稍微小一点留出更多空间
      const chartSize = Math.min(canvasWidth, canvasHeight) * chartScale
      const chartX = (canvasWidth - chartSize) / 2
      const chartY = (canvasHeight - chartSize) / 2 + 30 // 向下偏移一点，为标题留空间

      // 绘制图表（保持正方形比例）
      ctx.drawImage(chartImg, chartX, chartY, chartSize, chartSize)

      // 加载并绘制头像
      let avatarImg = null
      if (avatarSrc) {
        try {
          avatarImg = await loadImage(avatarSrc)
        } catch (error) {
          console.warn('头像加载失败，使用备选方案:', error)
        }
      }

      drawAvatar(ctx, avatarImg, canvasWidth, canvasHeight, username)

      // 添加标题文字（可选）
      if (userData.score !== undefined) {
        ctx.fillStyle = '#162D4E'
        ctx.font = 'bold 24px Arial'
        ctx.textAlign = 'center'
        ctx.fillText(`学术能力评估: ${userData.score}/10 分`, canvasWidth / 2, 50)
      }

      // 导出图片
      const mimeType = format === 'jpeg' ? 'image/jpeg' : 'image/png'
      const dataURL = canvas.toDataURL(mimeType, quality)

      // 创建下载链接
      const link = document.createElement('a')
      link.download = `academic-ability-${username || 'result'}.${format}`
      link.href = dataURL

      // 触发下载
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      // 显示成功提示
      toast.add({
        title: '导出成功',
        description: '图片已保存到下载文件夹',
        color: 'green',
        timeout: 3000,
      })

      return true
    } catch (error) {
      console.error('图片导出失败:', error)
      toast.add({
        title: '导出失败',
        description: error.message || '请稍后重试',
        color: 'red',
        timeout: 5000,
      })
      return false
    }
  }

  return {
    exportAsImage,
    generateImageCanvas,
    loadImage,
    drawBackground,
    drawAvatar,
  }
}
