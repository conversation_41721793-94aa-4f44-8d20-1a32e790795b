interface evaluateAcademicLevelResponse {
  username: string
  evaluation: any
  metadata: {
    tweets_analyzed: number
    user_info_available: boolean
  }
}

export default defineEventHandler(async (event) => {
  console.log('jin lai le 111111111111')
  const { username } = getQuery(event)

  //https://api.flizaos.com/evaluate_academic_level
  const ev: evaluateAcademicLevelResponse = await $fetch<evaluateAcademicLevelResponse>('https://api.flizaos.com/evaluate_academic_level?username=' + username + '&tweet_limit=50').catch((error) => {
    console.error('Error fetching profile image:', error)
    return {
      username: username as string,
      evaluation: null,
      metadata: {
        tweets_analyzed: 0,
        user_info_available: false,
      },
    }
  })
  console.log('Evaluation result:', ev)

  return {
    evaluation: ev.evaluation,
  }
})
